---
title: List locations
description: List locations endpoint documentation.
---


Relationships can be included via the `include` query parameter.

### Request endpoint

```http
GET /{companyId}/locations
```

Base URL: `https://cloudapi.inflowinventory.com`

### Path parameters

| Parameter | Type          | Required | Description                   |
|-----------|---------------|----------|-------------------------------|
| companyId | string <uuid> | yes      | Your inFlow account companyId |

### Query parameters

| Parameter            | Type              | Description               |
|----------------------|-------------------|---------------------------|
| request              | object (GetCollectionRequest) | Additional query parameter options |
| request.includeCount | boolean           | Return count in X-listCount header |
| request.count        | integer (int32)   | Max 100 per request       |
| request.after        | string (Nullable) | Entity ID for pagination  |
| request.before       | string (Nullable) | Entity ID for pagination  |
| request.start        | string (Nullable) | Include this entity and everything after |
| request.skip         | integer (int32)   | Number of records to skip |
| request.sort         | string (Nullable) | Property name to sort by  |
| request.sortDesc     | boolean           | If true, sort descending  |

### Response

#### Success response (200) schema: `application/json`

Array of `Location` objects:

| Field      | Type                | Description     |
|------------|---------------------|-----------------|
| address    | object (Address)    |                 |
| isActive   | boolean             | Locations with `IsActive = false` are deactivated and hidden away for new usage, but inventory is not removed from that location. |
| isDefault  | boolean             | Only one location, your company-wide default, should have `IsDefault = true`. |
| locationId | string <uuid>       | The primary identifier for this location. [When inserting, you should specify this by generating a GUID](../overview/index.md#write-requests). Not shown to users |
| name       | string              | Human-readable name for this location. A location most typically represents a warehouse or store. |
| timestamp  | string <rowversion> | You can optionally include the last-known timestamp when modifying to protect against concurrent modifications. |

#### Success response (200) example

##### Content type: `application/json`

```json
[
  {
    "address": {
      "address1": "36 Wonderland Ave.",
      "address2": "Unit 207",
      "addressType": "Commercial",
      "city": "Toronto",
      "country": "Canada",
      "postalCode": "90210",
      "remarks": "string",
      "state": "Ontario"
    },
    "isActive": true,
    "isDefault": true,
    "locationId": "00000000-0000-0000-0000-000000000000",
    "name": "string",
    "timestamp": "0000000000310AB6"
  }
]
```
