---
title: List all subscribed webhooks.
description: List all subscribed webhooks. endpoint documentation.
---


### Request endpoint

```http
GET /{companyId}/webhooks
```

Base URL: https://cloudapi.inflowinventory.com

### Path parameters

| Parameter | Type          | Required | Description                   |
|-----------|---------------|----------|-------------------------------|
| companyId | string <uuid> | Yes      | Your inFlow account companyId |

### Response

#### Success response (200) schema: `application/json`

Array of WebHookSubscription objects. Each WebHookSubscription object has the following properties:

| Field                | Type              | Description               |
|----------------------|-------------------|---------------------------|
| events               | Array of strings  | A list of events that will trigger this webhook. |
| secret               | string            | The secret key associated with this webhook subscription, used to generate the [HMAC header that is included with each webhook request](../web-hooks/index.md). Only returned at creation. |
| url                  | string            | Webhook URL to call when an event gets triggered. |
| webHookSubscriptionId | string <uuid>    | The primary identifier for this webhook. [When inserting, you should specify this by generating a GUID](../overview/index.md#write-requests). |

Valid values for `events` property:
- "customer.created"
- "customer.updated"
- "vendor.created"
- "vendor.updated"
- "purchaseOrder.created"
- "purchaseOrder.updated"
- "salesOrder.created"
- "salesOrder.updated"

#### Success response (200) example

##### Content type: `application/json`

```json
[
  {
    "events": [
      "string"
    ],
    "secret": "string",
    "url": "string",
    "webHookSubscriptionId": "00000000-0000-0000-0000-000000000000"
  }
]
```
