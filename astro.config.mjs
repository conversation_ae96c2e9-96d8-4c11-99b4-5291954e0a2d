// @ts-check
import { defineConfig } from "astro/config";
import starlight from "@astrojs/starlight";
import starlightThemeRapidePlugin from "starlight-theme-rapide";

// https://astro.build/config
export default defineConfig({
	integrations: [
		starlight({
			plugins: [starlightThemeRapidePlugin()],
			title: "Inflow API",
			social: [
				{
					icon: "github",
					label: "GitHub",
					href: "https://github.com/withastro/starlight",
				},
			],
			sidebar: [
				{
					label: "Get started",
					items: [
						{ label: "Overview", slug: "overview", link: "overview/index.md" },
					],
				},
				{
					label: "Adjustment Reason",
					items: [
						{
							label: "Get adjustment reasons",
							slug: "adjustment-reason/get-adjustment-reasons",
							link: "adjustment-reason/get-adjustment-reasons.md",
						},
						{
							label: "List adjustment reasons",
							slug: "adjustment-reason/list-adjustment-reasons",
							link: "adjustment-reason/list-adjustment-reasons.md",
						},
					],
				},
				{
					label: "Categories",
					items: [
						{
							label: "Get a category",
							slug: "categories/get-a-category",
							link: "categories/get-a-category.md",
						},
						{
							label: "List categories",
							slug: "categories/list-categories",
							link: "categories/list-categories.md",
						},
					],
				},
			],
		}),
	],
});
